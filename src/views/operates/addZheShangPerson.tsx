import {
  Button,
  Form,
  Icon,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Radio,
  Tooltip,
  message,
} from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { opApi } from '@app/api';
import { A, Drawer, ImageUploader } from '@app/components/common';
import { useDispatch } from 'react-redux';
import { setConfig } from '@app/action/config';
import uuid from 'uuid';
import NewNewsSearchAndInput from '@app/components/common/newNewsSearchAndInput';
import { getImageRatio } from '@app/utils/utils';

const AddPartyNewsDrawer = (props: any, ref: any) => {
  const dispatch = useDispatch();

  const [page_list, setPageList] = useState<any>([]);
  const [recordInfo, setRecordInfo] = useState<any>({
    visible: false,
    titleName: '',
    key: '',
    pid: '',
    index: '',
    value: '',
    sizeMax: 40,
  });
  const { getFieldDecorator, getFieldsValue, setFieldsValue } = props.form;
  useImperativeHandle(
    ref,
    () => ({
      ...props.form,
    }),
    []
  );

  const formLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 18 },
  };

  // 初始化页面数据
  const intPageList = () => {
    if (props.record) {
      // ✅ 处理从后端接收的group数据（可能是JSON字符串）
      let groupData = props.record.group;
      if (typeof groupData === 'string') {
        try {
          groupData = JSON.parse(groupData);
        } catch (error) {
          console.error('解析group JSON数据失败:', error);
          groupData = [];
        }
      }

      return (
        groupData?.map((group: any) => {
          const pid = uuid();
          return {
            pid,
            group_name: group.group_name,
            articles:
              group.articles?.map((article: any) => {
                return {
                  ...article,
                  // ✅ 字段映射以匹配内部使用结构
                  id: article.article_id, // article_id -> id (内部使用)
                  list_title: article.list_title, // 保持原始标题
                  customize_pic_url: article.pic_url, // pic_url -> customize_pic_url (内部使用)
                  custom_title: article.customize_title, // customize_title -> custom_title (内部使用)
                  pid,
                };
              }) || [],
          };
        }) || []
      );
    } else {
      return [
        {
          pid: uuid(),
          group_name: '',
          articles: [],
        },
      ];
    }
  };

  useEffect(() => {
    setPageList(intPageList());
  }, [props.visible]);

  useEffect(() => {
    const { page_list: arr } = getFieldsValue();
    if (arr) {
      setTimeout(() => {
        setFieldsValue({ page_list: page_list });
      }, 0);
    }
  }, [page_list]);

  const findPageWithPID = (list: any, id: any): any => {
    return list?.filter((item: any) => item.pid == id)?.[0];
  };

  const handleSubmit = () => {
    // e.preventDefault();
    props.form.validateFieldsAndScroll((err: any, values: any) => {
      if (!err) {
        dispatch(setConfig({ mLoading: true }));
        let promise = null;
        const parmas: any = {
          // 浙商人物基本信息 - 使用新的字段名称
          name: values.name?.trim(),
          background_url: values.background_url,
          top_title: values.top_title?.trim(),
          detail_url: values.detail_url?.trim() || '',
          introduction: values.introduction?.trim(),
          country: values.country?.trim() || '',
          gender: values.gender, // 数字类型：1-男，2-女，0-未知
          ethnicity: values.ethnicity?.trim() || '',
          graduate_school: values.graduate_school?.trim() || '',
          occupation: values.occupation?.trim() || '',
          positions: values.positions?.trim() || '',
          company: values.company?.trim() || '',
          group: values.page_list.map((item: any) => {
            return {
              group_name: item.group_name,
              articles: item.articles.map((cItem: any, index: number) => {
                return {
                  position: index + 1, // 位置字段
                  article_id: cItem.id, // 文章ID
                  list_title: cItem.list_title, // 原始标题
                  customize_title: cItem.custom_title, // 自定义标题
                  pic_url: cItem.customize_pic_url, // 图片URL
                };
              }),
            };
          }),
          share_title: values.share_title?.trim() || '',
          share_subtitle: values.share_subtitle?.trim() || '',
        };
        if (!props.record) {
          // 新增
          promise = opApi.createZheShangPerson(parmas);
        } else {
          parmas.zj_business_person_id = props.record.zj_business_person_id;
          promise = opApi.editZheShangPerson(parmas);
        }
        promise
          .then((res: any) => {
            message.success(!props.record ? '新增成功' : '更新成功');
            dispatch(setConfig({ mLoading: false }));
            props.onEnd && props.onEnd();
          })
          .catch(() => {
            // message.error('添加失败');
            dispatch(setConfig({ mLoading: false }));
          });
      } else {
        message.error('请检查表单内容');
        dispatch(setConfig({ mLoading: false }));
      }
    });
  };

  const columns = [
    { title: '潮新闻ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: '新闻频道', dataIndex: 'channel_name', key: 'channel_name', width: 80 },
    {
      title: '新闻标题',
      dataIndex: 'list_title',
      key: 'list_title',
      width: 100,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
          <span
            style={{
              display: '-webkit-box',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 5,
              WebkitBoxOrient: 'vertical',
              maxWidth: '80px',
            }}
            title={text}
          >
            {text}
          </span>
        </div>
      ),
    },
    {
      title: '自定义标题',
      dataIndex: 'custom_title',
      key: 'custom_title',
      width: 120,
      render: (text: string, record: any, index: number) => (
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <span
            style={{
              display: '-webkit-box',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 5,
              WebkitBoxOrient: 'vertical',
              maxWidth: '76px',
            }}
            title={text}
          >
            {text}
          </span>
          <A onClick={() => handleOperate('custom_title', text, record.pid, index)}>编辑</A>
        </div>
      ),
    },
    {
      title: '列表图',
      dataIndex: 'customize_pic_url',
      key: 'customize_pic_url',
      width: 114,
      render: (text: string, record: any, index: number) => (
        <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
          {text && <img src={text} style={{ height: 40, maxWidth: 70 }} />}
          <A onClick={() => editPic(text, record, index)}>{text ? '修改' : '上传'}</A>
        </div>
      ),
    },
  ];

  const delectList = (id: any) => {
    const { page_list: arr } = getFieldsValue();
    const arrayConment = arr.filter((item: { pid: any }) => {
      return item.pid !== id;
    });
    setTimeout(() => {
      // console.log(arrayConment)
      setFieldsValue({ page_list: arrayConment });
      // console.log(getFieldsValue())
    }, 0);
    setPageList(arrayConment);
  };

  const handleMove = (data: {}, index: number) => {
    let position = index + 1;
    Modal.confirm({
      width: 250,
      content: (
        <>
          位置:{' '}
          <InputNumber
            min={1}
            max={page_list.length}
            defaultValue={position}
            onChange={(e: any) => {
              position = e;
            }}
          />
        </>
      ),
      icon: null,
      onOk() {
        const { page_list: arr } = getFieldsValue();
        let item = arr[index];
        let newArray = [...arr];
        newArray.splice(index, 1);
        newArray.splice(position - 1, 0, item);
        setPageList(newArray);
      },
      onCancel() {},
    });
  };

  // 编辑
  const handleOperate = (key: any, value: any, pid: any, index: any) => {
    setRecordInfo({
      visible: true,
      titleName: key === 'custom_title' ? '自定义标题' : '摘要',
      sizeMax: key === 'custom_title' ? 40 : 60,
      pid,
      index,
      key,
      value,
    });
  };

  const infoSubmit = () => {
    if (!recordInfo.value) return message.error('请填写内容');

    const pageList = getFieldsValue().page_list;
    const { pid, index, key, value } = recordInfo;
    const page = findPageWithPID(pageList, pid);
    if (page) {
      page.articles[index][key] = value;
    }
    setFieldsValue({ page_list: pageList });
    setRecordInfo({
      visible: false,
    });
  };

  const editPic = async (url: string, record: any, index: number) => {
    let pic: string = url;
    let modal: any;
    // const { divisor: w, dividend: h } = proportionHandle(record, index)
    let proportion = 4 / 3;
    let consistentProportions = true;
    if (url) {
      const afterCalculation: any = await getImageRatio(url);
      console.log('xxx', afterCalculation, afterCalculation - proportion);
      if (Math.abs(afterCalculation - proportion) <= 0.05) {
        consistentProportions = false;
      } else {
        consistentProportions = true;
      }
    }

    const picChange = (u: string) => {
      console.log('xxx', u);
      pic = u;
      modal.update({
        content: (
          <>
            <ImageUploader value={pic} onChange={picChange} ratio={4 / 3} />
            <p>比例4:3</p>
          </>
        ),
      });
    };
    modal = Modal.confirm({
      title: '编辑图片',
      width: 500,
      content: (
        <>
          <ImageUploader
            value={pic}
            onChange={picChange}
            imgMaxWidth={400}
            // isCutting={true}
            ratio={4 / 3}
          />
          {!!url && consistentProportions ? (
            <span style={{ color: 'red' }}>
              该图片比例非4:3，图片将自动居中截取，建议重新上传。
            </span>
          ) : (
            <p>建议比例4:3</p>
          )}
        </>
      ),
      onOk: async (destroy: Function) => {
        // if (!pic) {
        //   message.error('请上传图片');
        //   return;
        // }
        const pageList = getFieldsValue().page_list;
        const page = findPageWithPID(pageList, record.pid);
        if (page) {
          page.articles[index].customize_pic_url = pic;
        }
        setFieldsValue({ page_list: pageList });
        destroy();
      },
    });
  };

  const addGroup = () => {
    const { page_list: articleList = [] } = getFieldsValue();
    const result = [
      ...articleList,
      {
        pid: uuid(),
        group_name: '', // ✅ 修正字段名：list_title -> group_name
        articles: [],
      },
    ];
    setPageList(result);
  };

  return (
    <Drawer
      title={!props.record ? '添加/编辑人物' : '编辑人物'}
      visible={props.visible}
      skey={props.skey}
      onClose={props.onClose}
      maskClosable={false}
      // width={500}
      onOk={handleSubmit}
      okText="保存"
    >
      <Form {...formLayout}>
        {/* 顶部信息分组 */}
        <p style={{ height: '40px', lineHeight: '40px', fontWeight: 600 }}>顶部信息</p>

        <Form.Item
          label={
            <>
              <Tooltip
                placement="left"
                title={<img width={187} height={214} src="/assets/character_photo_tip.png"></img>}
              >
                <Icon type="question-circle" style={{ position: 'absolute', left: -20, top: 0 }} />
              </Tooltip>
              背景图
            </>
          }
          extra="支持jpg,jpeg,png图片格式，建议比例375:445"
        >
          {getFieldDecorator('background_url', {
            initialValue: props.record?.background_url,
            rules: [
              {
                required: true,
                message: '请上传背景图',
              },
            ],
          })(<ImageUploader ratio={375 / 445} accept={['image/jpeg', 'image/png', 'image/jpg']} />)}
        </Form.Item>

        <Form.Item label="顶部标题">
          {getFieldDecorator('top_title', {
            initialValue: props.record?.top_title,
            rules: [
              {
                required: true,
                message: '请填写顶部标题',
                whitespace: true,
              },
              {
                max: 30,
                message: '最多 30 字',
              },
            ],
          })(<Input maxLength={30} placeholder="最多 30 字" />)}
        </Form.Item>

        <Form.Item label="跳转链接">
          {getFieldDecorator('detail_url', {
            initialValue: props.record?.detail_url,
            rules: [
              {
                type: 'url',
                message: '请输入正确的链接格式',
              },
            ],
          })(<Input placeholder="请输入跳转链接（选填）" />)}
        </Form.Item>

        {/* 人物信息分组 */}
        <p style={{ height: '40px', lineHeight: '40px', fontWeight: 600 }}>人物信息</p>

        <Form.Item label="姓名">
          {getFieldDecorator('name', {
            initialValue: props.record?.name,
            rules: [
              {
                required: true,
                message: '请填写姓名',
                whitespace: true,
              },
              {
                max: 15,
                message: '最多 15 字',
              },
            ],
          })(<Input maxLength={15} placeholder="最多 15 字" />)}
        </Form.Item>

        <Form.Item label="一句话简介">
          {getFieldDecorator('introduction', {
            initialValue: props.record?.introduction,
            rules: [
              {
                required: true,
                message: '请填写一句话简介',
                whitespace: true,
              },
              {
                max: 20,
                message: '最多 20 字',
              },
            ],
          })(<Input maxLength={20} placeholder="最多 20 字" />)}
        </Form.Item>

        <Form.Item label="国籍">
          {getFieldDecorator('country', {
            initialValue: props.record?.country,
            rules: [
              {
                max: 10,
                message: '最多 10 字',
              },
            ],
          })(<Input maxLength={10} placeholder="最多 10 字（选填）" />)}
        </Form.Item>

        <Form.Item label="性别">
          {getFieldDecorator('gender', {
            initialValue: props.record?.gender,
            rules: [
              {
                required: true,
                message: '请选择性别',
              },
            ],
          })(
            <Radio.Group>
              <Radio value={1}>男</Radio>
              <Radio value={2}>女</Radio>
            </Radio.Group>
          )}
        </Form.Item>

        <Form.Item label="民族">
          {getFieldDecorator('ethnicity', {
            initialValue: props.record?.ethnicity,
            rules: [
              {
                max: 10,
                message: '最多 10 字',
              },
            ],
          })(<Input maxLength={10} placeholder="最多 10 字（选填）" />)}
        </Form.Item>

        <Form.Item label="毕业院校">
          {getFieldDecorator('graduate_school', {
            initialValue: props.record?.graduate_school,
            rules: [
              {
                max: 20,
                message: '最多 20 字',
              },
            ],
          })(<Input maxLength={20} placeholder="最多 20 字（选填）" />)}
        </Form.Item>

        <Form.Item label="职业">
          {getFieldDecorator('occupation', {
            initialValue: props.record?.occupation,
            rules: [
              {
                max: 20,
                message: '最多 20 字',
              },
            ],
          })(<Input maxLength={20} placeholder="最多 20 字（选填）" />)}
        </Form.Item>

        <Form.Item label="职务">
          {getFieldDecorator('positions', {
            initialValue: props.record?.positions || props.record?.position, // 向后兼容
            rules: [
              {
                max: 20,
                message: '最多 20 字',
              },
            ],
          })(<Input maxLength={20} placeholder="最多 20 字（选填）" />)}
        </Form.Item>

        <Form.Item label="工作单位">
          {getFieldDecorator('company', {
            initialValue: props.record?.company || props.record?.workplace, // 向后兼容
            rules: [
              {
                max: 20,
                message: '最多 20 字',
              },
            ],
          })(<Input maxLength={20} placeholder="最多 20 字（选填）" />)}
        </Form.Item>

        {page_list.map(
          (item: { articles: any; pid: string; group_name: string }, index: number) => {
            return (
              <div key={item.pid + `${index}`}>
                <Form.Item label="id" style={{ display: 'none' }}>
                  {getFieldDecorator(`page_list[${index}].pid`, {
                    initialValue: item.pid,
                  })(<Input disabled />)}
                </Form.Item>
                <Form.Item>
                  <h3 style={{ marginLeft: 50 }}>分组{index + 1}</h3>
                </Form.Item>

                <Form.Item label="分组名称">
                  {getFieldDecorator(`page_list[${index}].group_name`, {
                    initialValue: item.group_name,
                    validateTrigger: 'onChange',
                    rules: [
                      {
                        required: true,
                        message: '请输入分组名称',
                        whitespace: true,
                      },
                    ],
                  })(<Input placeholder="最多输入10字" maxLength={10} />)}
                  {page_list.length > 1 && (
                    <div
                      style={{
                        display: 'flex',
                        position: 'absolute',
                        width: 60,
                        top: '-12px',
                        right: '-60px',
                      }}
                    >
                      <div
                        style={{
                          height: '40px',
                          lineHeight: '40px',
                          width: '40px',
                          textAlign: 'center',
                          cursor: 'pointer',
                        }}
                      >
                        <Icon
                          type="swap"
                          style={{ transform: 'rotate(90deg)' }}
                          onClick={() => {
                            handleMove(item, index);
                          }}
                        />
                      </div>

                      <div
                        onClick={() => {}}
                        style={{
                          height: '40px',
                          lineHeight: '40px',
                          width: '40px',
                          textAlign: 'center',
                          cursor: 'pointer',
                        }}
                      >
                        <Popconfirm
                          placement="top"
                          title="确定要删除吗？"
                          okText="确定"
                          cancelText="取消"
                          icon={
                            <Icon type="exclamation-circle" theme="twoTone" twoToneColor="red" />
                          }
                          onConfirm={() => {
                            delectList(item.pid);
                          }}
                        >
                          <Icon type="delete" />
                        </Popconfirm>
                      </div>
                    </div>
                  )}
                </Form.Item>
                <Form.Item
                  label={
                    <>
                      <Tooltip placement="left" title={'仅支持关联媒立方稿件'}>
                        <Icon
                          type="question-circle"
                          style={{ position: 'absolute', left: -20, top: 0 }}
                        />
                      </Tooltip>
                      关联稿件
                    </>
                  }
                >
                  {getFieldDecorator(`page_list[${index}].articles`, {
                    initialValue: item.articles || [],
                    validateTrigger: 'onChange',
                    rules: [
                      {
                        required: true,
                        message: '请添加关联稿件',
                        type: 'array',
                      },
                      {
                        max: 10,
                        message: `最多关联10条稿件`,
                        type: 'array',
                      },
                      {
                        min: 1,
                        message: `请至少关联1条稿件`,
                        type: 'array',
                      },
                      {
                        validator: (rule: any, val: any, callback: any) => {
                          if (!val) {
                            return callback('');
                          } else if (val.filter((v: any) => !v.custom_title).length > 0) {
                            return callback('请填写自定义标题');
                            // } else if (val.filter((v: any) => !v.customize_pic_url).length > 0) {
                            //   return callback('请上传列表图');
                            // 可以不上传列表图
                          } else {
                            return callback();
                          }
                        },
                      },
                    ],
                  })(
                    <NewNewsSearchAndInput
                      max={10}
                      func="listArticleRecommendSearch"
                      columns={columns}
                      // customOp={true}
                      placeholder="输入ID或标题关联稿件"
                      // orderChange={orderChange}
                      body={{ doc_types: '2,3,4,5,8,9' }}
                      order={true}
                      // isMedia={true}
                      map={(article: any) => {
                        const list_pics = article.list_pics.split(',')[0] || null;
                        return {
                          ...article,
                          customize_pic_url: list_pics || null,
                          custom_title: article.list_title || article.doc_title,
                          pid: item.pid,
                        };
                      }}
                    />
                  )}
                </Form.Item>
              </div>
            );
          }
        )}

        {page_list.length < 10 && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 20,
            }}
          >
            <Button style={{ width: 500, marginRight: 8 }} onClick={addGroup}>
              <Icon type="plus-circle-o" />
              添加分组
            </Button>
            <Tooltip title="最多添加10个分组">
              <Icon type="question-circle" />
            </Tooltip>
          </div>
        )}
        <p style={{ height: '40px', lineHeight: '40px', fontWeight: 600 }}>分享信息</p>
        <Form.Item label="分享标题">
          {getFieldDecorator('share_title', {
            initialValue: props.record?.share_title || props.record?.ref_ids2 || '', // 向后兼容
            rules: [
              {
                required: false,
                message: '请填写分享标题',
              },
            ],
          })(<Input placeholder="最多24字，如不填写，默认显示合集标题" maxLength={24} />)}
        </Form.Item>
        <Form.Item label="分享副标题">
          {getFieldDecorator('share_subtitle', {
            initialValue: props.record?.share_subtitle || props.record?.ref_ids3 || '', // 向后兼容
            rules: [
              {
                required: false,
                message: '请填写分享副标题',
              },
            ],
          })(
            <Input placeholder="最多30字，如不填写，默认显示「来自潮新闻客户端」" maxLength={30} />
          )}
        </Form.Item>
      </Form>

      <Modal
        visible={recordInfo.visible}
        onCancel={() => {
          setRecordInfo({ visible: false });
        }}
        onOk={() => {
          infoSubmit();
        }}
        width={650}
      >
        <p>
          <span style={{ color: 'red' }}>*</span> &nbsp;{recordInfo.titleName}
        </p>
        <Input.TextArea
          rows={4}
          value={recordInfo.value}
          maxLength={recordInfo.sizeMax}
          placeholder={`最多输入${recordInfo.sizeMax}字`}
          onChange={(e) => {
            setRecordInfo({
              ...recordInfo,
              value: e.target.value,
            });
          }}
        />
      </Modal>
    </Drawer>
  );
};

export default Form.create<any>({ name: 'AddPartyNewsDrawer' })(
  forwardRef<any, any>(AddPartyNewsDrawer)
);
